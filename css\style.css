/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    line-height: 1.6;
    color: #333;
    direction: rtl;
    text-align: right;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Main Section Styles */
.main-section {
    background: linear-gradient(135deg, #1e3a8a 0%, #dc3545 100%);
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    padding: 2rem;
    color: white;
}

.main-container {
    max-width: 800px;
    width: 100%;
    text-align: center;
    animation: fadeInUp 1s ease;
}

/* Company Header Styles */
.company-header {
    text-align: center;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease 0.2s both;
}

/* Main Content Area */
.main-content-area {
    display: grid;
    grid-template-columns: 300px 1fr;
    gap: 3rem;
    align-items: start;
    margin-bottom: 3rem;
    animation: fadeInUp 1s ease 0.4s both;
}

/* Company Logo Styles */
.company-logo {
    display: flex;
    justify-content: center;
    align-items: center;
}

.logo-placeholder {
    width: 250px;
    height: 250px;
    position: relative;
    border-radius: 20px;
    overflow: hidden;
}

.logo-upload-area {
    width: 100%;
    height: 100%;
    background: rgba(255, 255, 255, 0.15);
    backdrop-filter: blur(10px);
    border: 3px dashed rgba(255, 255, 255, 0.4);
    border-radius: 20px;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all 0.3s ease;
    animation: pulse 3s infinite;
}

.logo-upload-area:hover {
    background: rgba(255, 255, 255, 0.25);
    border-color: rgba(255, 255, 255, 0.6);
    transform: scale(1.02);
}

.upload-icon {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 1rem;
    text-shadow: 0 0 20px rgba(220, 53, 69, 0.5);
}

.upload-text {
    color: white;
    font-size: 1rem;
    font-weight: 600;
    text-align: center;
    margin: 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

.logo-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    border-radius: 20px;
    box-shadow: 0 10px 30px rgba(0,0,0,0.3);
    transition: all 0.3s ease;
}

.logo-image:hover {
    transform: scale(1.05);
    box-shadow: 0 15px 40px rgba(0,0,0,0.4);
}

/* Change Logo Button */
.change-logo-btn {
    position: absolute;
    bottom: -15px;
    left: 50%;
    transform: translateX(-50%);
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    border: none;
    padding: 0.5rem 1rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
    cursor: pointer;
    transition: all 0.3s ease;
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
    font-family: 'Cairo', sans-serif;
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.change-logo-btn:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    transform: translateX(-50%) translateY(-2px);
    box-shadow: 0 8px 20px rgba(220, 53, 69, 0.4);
}

.change-logo-btn i {
    font-size: 0.9rem;
}

.company-name {
    font-size: 3rem;
    font-weight: 700;
    margin-bottom: 0.5rem;
    text-shadow: 2px 2px 4px rgba(0,0,0,0.3);
}

.company-name-en {
    font-size: 1.5rem;
    font-weight: 400;
    color: #ffd700;
    margin-bottom: 0;
    text-shadow: 1px 1px 2px rgba(0,0,0,0.3);
}

/* Commercial Record Styles */
.commercial-record {
    flex: 1;
}

.record-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 2rem;
    box-shadow: 0 20px 40px rgba(0,0,0,0.2);
    color: #333;
    border: 1px solid rgba(255, 255, 255, 0.3);
    transition: all 0.3s ease;
}

.record-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 25px 50px rgba(0,0,0,0.3);
}

.record-header {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 2rem;
    padding-bottom: 1rem;
    border-bottom: 2px solid #dc3545;
}

.record-icon {
    font-size: 2rem;
    color: #dc3545;
}

.record-header h3 {
    font-size: 1.8rem;
    font-weight: 700;
    color: #1e3a8a;
    margin: 0;
}

.record-content {
    display: grid;
    gap: 1rem;
    margin-bottom: 2rem;
}

.record-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 10px;
    border-left: 4px solid #dc3545;
    transition: all 0.3s ease;
}

.record-item:hover {
    background: #e9ecef;
    transform: translateX(-5px);
}

.record-label {
    font-weight: 600;
    color: #1e3a8a;
    font-size: 1rem;
}

.record-value {
    font-weight: 500;
    color: #333;
    font-size: 1rem;
    text-align: left;
}

.record-footer {
    text-align: center;
}

.verification-badge {
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    background: linear-gradient(135deg, #28a745 0%, #20c997 100%);
    color: white;
    padding: 0.75rem 1.5rem;
    border-radius: 25px;
    font-weight: 600;
    box-shadow: 0 5px 15px rgba(40, 167, 69, 0.3);
}

.verification-badge i {
    font-size: 1.2rem;
}

/* Action Buttons */
.action-buttons {
    display: flex;
    gap: 1.5rem;
    justify-content: center;
    flex-wrap: wrap;
    animation: fadeInUp 1s ease 0.6s both;
    margin-top: 2rem;
}



.btn {
    padding: 1rem 2rem;
    border: none;
    border-radius: 50px;
    font-size: 1.1rem;
    font-weight: 600;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    transition: all 0.3s ease;
    cursor: pointer;
    min-width: 180px;
    justify-content: center;
}

.btn-primary {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%);
    color: white;
    box-shadow: 0 5px 15px rgba(220,53,69,0.3);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #c82333 0%, #a71e2a 100%);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(220,53,69,0.4);
}

.btn-secondary {
    background: rgba(255, 255, 255, 0.1);
    color: white;
    border: 2px solid rgba(255, 255, 255, 0.3);
    backdrop-filter: blur(10px);
}

.btn-secondary:hover {
    background: rgba(255, 255, 255, 0.2);
    border-color: rgba(255, 255, 255, 0.5);
    transform: translateY(-3px);
    box-shadow: 0 10px 25px rgba(255,255,255,0.2);
}

/* Services Section */
.services {
    padding: 5rem 0;
    background: #f8f9fa;
}

.section-title {
    text-align: center;
    font-size: 2.5rem;
    font-weight: 700;
    margin-bottom: 3rem;
    color: #333;
    position: relative;
}

.section-title::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 80px;
    height: 4px;
    background: linear-gradient(135deg, #dc3545 0%, #1e3a8a 100%);
    border-radius: 2px;
}

.services-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 2rem;
}

.service-card {
    background: white;
    padding: 2.5rem;
    border-radius: 15px;
    text-align: center;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    border-top: 4px solid transparent;
}

.service-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 15px 40px rgba(0,0,0,0.15);
    border-top-color: #dc3545;
}

.service-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, #dc3545 0%, #1e3a8a 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1.5rem;
    font-size: 2rem;
    color: white;
}

.service-card h3 {
    font-size: 1.5rem;
    font-weight: 600;
    margin-bottom: 1rem;
    color: #333;
}

.service-card p {
    color: #666;
    line-height: 1.6;
}

/* About Section */
.about {
    padding: 5rem 0;
    background: white;
}

.about-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
    align-items: center;
}

.about-text p {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #666;
    margin-bottom: 1.5rem;
}

.about-features {
    margin-top: 2rem;
}

.feature {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.feature i {
    color: #dc3545;
    font-size: 1.2rem;
}

.about-icon {
    font-size: 12rem;
    color: #1e3a8a;
    opacity: 0.1;
}

/* Contact Section */
.contact {
    padding: 5rem 0;
    background: #f8f9fa;
}

.contact-content {
    display: grid;
    grid-template-columns: 1fr 1fr;
    gap: 3rem;
}

.contact-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 2rem;
}

.contact-item i {
    width: 50px;
    height: 50px;
    background: linear-gradient(135deg, #dc3545 0%, #1e3a8a 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1.2rem;
}

.contact-item h4 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.contact-item p {
    color: #666;
}

.contact-form {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    box-shadow: 0 5px 20px rgba(0,0,0,0.1);
}

.form-group {
    margin-bottom: 1.5rem;
}

.form-group input,
.form-group textarea {
    width: 100%;
    padding: 1rem;
    border: 2px solid #e9ecef;
    border-radius: 10px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: border-color 0.3s ease;
}

.form-group input:focus,
.form-group textarea:focus {
    outline: none;
    border-color: #dc3545;
}

/* Footer */
.footer {
    background: #2c3e50;
    color: white;
    padding: 3rem 0 1rem;
}

.footer-content {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 2rem;
    margin-bottom: 2rem;
}

.footer-section h4 {
    font-size: 1.3rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.footer-section ul {
    list-style: none;
}

.footer-section ul li {
    margin-bottom: 0.5rem;
}

.footer-section ul li a {
    color: #bdc3c7;
    text-decoration: none;
    transition: color 0.3s ease;
}

.footer-section ul li a:hover {
    color: white;
}

.social-links {
    display: flex;
    gap: 1rem;
}

.social-links a {
    width: 40px;
    height: 40px;
    background: #34495e;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    text-decoration: none;
    transition: all 0.3s ease;
}

.social-links a:hover {
    background: #dc3545;
    transform: translateY(-3px);
}

.footer-bottom {
    text-align: center;
    padding-top: 2rem;
    border-top: 1px solid #34495e;
    color: #bdc3c7;
}

/* Animations */
@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

/* Responsive Design */
@media (max-width: 1024px) {
    .main-content-area {
        grid-template-columns: 1fr;
        gap: 2rem;
        text-align: center;
    }

    .logo-placeholder {
        width: 200px;
        height: 200px;
        margin: 0 auto;
    }
}

@media (max-width: 768px) {
    .main-section {
        padding: 1rem;
        min-height: 100vh;
    }

    .main-container {
        max-width: 100%;
    }

    .main-content-area {
        grid-template-columns: 1fr;
        gap: 1.5rem;
        text-align: center;
    }

    .logo-placeholder {
        width: 180px;
        height: 180px;
        margin: 0 auto;
    }

    .upload-icon {
        font-size: 2.5rem;
    }

    .upload-text {
        font-size: 0.9rem;
    }

    .company-name {
        font-size: 2.5rem;
    }

    .company-name-en {
        font-size: 1.2rem;
    }

    .record-card {
        padding: 1.5rem;
        margin: 0;
    }

    .record-header h3 {
        font-size: 1.5rem;
    }

    .record-item {
        flex-direction: column;
        text-align: center;
        gap: 0.5rem;
    }

    .record-value {
        text-align: center;
    }

    .action-buttons {
        flex-direction: column;
        align-items: center;
        gap: 1rem;
    }

    .btn {
        width: 100%;
        max-width: 280px;
    }

    .about-content,
    .contact-content {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 480px) {
    .main-section {
        padding: 0.5rem;
    }

    .company-header {
        margin-bottom: 2rem;
    }

    .main-content-area {
        margin-bottom: 2rem;
        gap: 1rem;
    }

    .logo-placeholder {
        width: 150px;
        height: 150px;
    }

    .record-card {
        padding: 1rem;
    }

    .record-header {
        flex-direction: column;
        gap: 0.5rem;
    }

    .record-icon {
        font-size: 1.5rem;
    }

    .record-header h3 {
        font-size: 1.3rem;
    }

    .verification-badge {
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }
}

/* Ripple Effect for Buttons */
.btn {
    position: relative;
    overflow: hidden;
}

.ripple {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.6);
    transform: scale(0);
    animation: ripple-animation 0.6s linear;
    pointer-events: none;
}

@keyframes ripple-animation {
    to {
        transform: scale(4);
        opacity: 0;
    }
}

/* Enhanced Pulse Animation */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
        box-shadow: 0 0 0 0 rgba(220, 53, 69, 0.4);
    }
    50% {
        transform: scale(1.05);
        box-shadow: 0 0 0 20px rgba(220, 53, 69, 0);
    }
}

/* Floating Animation */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-10px);
    }
}

/* Glow Effect for Logo Upload Area */
.logo-upload-area::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: linear-gradient(45deg, #dc3545, #1e3a8a, #dc3545);
    border-radius: 20px;
    z-index: -1;
    opacity: 0;
    transition: opacity 0.3s ease;
}

.logo-upload-area:hover::before {
    opacity: 0.3;
    animation: rotate 2s linear infinite;
}

@keyframes rotate {
    0% {
        transform: rotate(0deg);
    }
    100% {
        transform: rotate(360deg);
    }
}

/* Enhanced Record Card Animation */
.record-card {
    position: relative;
    overflow: hidden;
}

.record-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(220, 53, 69, 0.1), transparent);
    transition: left 0.5s ease;
}

.record-card:hover::before {
    left: 100%;
}

/* Smooth Transitions */
* {
    transition: all 0.3s ease;
}

/* Custom Scrollbar */
::-webkit-scrollbar {
    width: 8px;
}

::-webkit-scrollbar-track {
    background: #f1f1f1;
}

::-webkit-scrollbar-thumb {
    background: linear-gradient(135deg, #dc3545 0%, #1e3a8a 100%);
    border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
    background: linear-gradient(135deg, #c82333 0%, #1e40af 100%);
}
