/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    background: #f8f9fa;
    overflow-x: hidden;
}

/* Layout */
.sidebar {
    position: fixed;
    top: 0;
    right: 0;
    width: 280px;
    height: 100vh;
    background: linear-gradient(180deg, #1e3a8a 0%, #dc3545 100%);
    color: white;
    z-index: 1000;
    transition: transform 0.3s ease;
    overflow-y: auto;
}

.sidebar.collapsed {
    transform: translateX(100%);
}

.main-content {
    margin-right: 280px;
    min-height: 100vh;
    transition: margin-right 0.3s ease;
}

.main-content.expanded {
    margin-right: 0;
}

/* Sidebar Header */
.sidebar-header {
    padding: 1.5rem;
    border-bottom: 1px solid rgba(255,255,255,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.logo {
    display: flex;
    align-items: center;
    gap: 0.75rem;
}

.logo-icon {
    font-size: 2rem;
    color: #dc3545;
}

.logo-text {
    font-size: 1.5rem;
    font-weight: 700;
}

.sidebar-toggle {
    background: none;
    border: none;
    color: white;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.sidebar-toggle:hover {
    background: rgba(255,255,255,0.1);
}

/* Sidebar Navigation */
.sidebar-nav {
    padding: 1rem 0;
}

.nav-list {
    list-style: none;
}

.nav-item {
    margin-bottom: 0.25rem;
}

.nav-link {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem 1.5rem;
    color: rgba(255,255,255,0.8);
    text-decoration: none;
    transition: all 0.3s ease;
    border-right: 3px solid transparent;
}

.nav-link:hover {
    background: rgba(255,255,255,0.1);
    color: white;
}

.nav-link.active {
    background: rgba(255,255,255,0.15);
    color: white;
    border-right-color: #dc3545;
}

.nav-link i {
    font-size: 1.1rem;
    width: 20px;
}

/* Sidebar Footer */
.sidebar-footer {
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    padding: 1.5rem;
    border-top: 1px solid rgba(255,255,255,0.1);
}

.user-info {
    display: flex;
    align-items: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.user-avatar {
    width: 40px;
    height: 40px;
    background: #dc3545;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
}

.user-details {
    flex: 1;
}

.user-name {
    display: block;
    font-weight: 600;
    font-size: 0.9rem;
}

.user-role {
    display: block;
    font-size: 0.8rem;
    color: rgba(255,255,255,0.7);
}

.logout-btn {
    width: 100%;
    background: #e74c3c;
    border: none;
    color: white;
    padding: 0.75rem;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: background 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
}

.logout-btn:hover {
    background: #c0392b;
}

/* Header */
.header {
    background: white;
    padding: 1rem 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 100;
}

.header-left {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.mobile-menu-btn {
    display: none;
    background: none;
    border: none;
    font-size: 1.2rem;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 5px;
    transition: background 0.3s ease;
}

.mobile-menu-btn:hover {
    background: #f8f9fa;
}

.page-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
}

.header-right {
    display: flex;
    align-items: center;
}

.header-actions {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.action-btn {
    position: relative;
    background: none;
    border: none;
    font-size: 1.2rem;
    color: #666;
    cursor: pointer;
    padding: 0.75rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.action-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.notification-badge {
    position: absolute;
    top: 5px;
    left: 5px;
    background: #e74c3c;
    color: white;
    font-size: 0.7rem;
    padding: 0.2rem 0.4rem;
    border-radius: 10px;
    min-width: 18px;
    text-align: center;
}

.user-menu-btn {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    background: none;
    border: none;
    cursor: pointer;
    padding: 0.5rem 1rem;
    border-radius: 8px;
    transition: background 0.3s ease;
    font-family: 'Cairo', sans-serif;
}

.user-menu-btn:hover {
    background: #f8f9fa;
}

.user-menu .user-avatar {
    width: 35px;
    height: 35px;
    font-size: 1rem;
}

/* Content Area */
.content-area {
    padding: 2rem;
}

.content-section {
    display: none;
}

.content-section.active {
    display: block;
}

/* Dashboard Stats */
.dashboard-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.stat-card {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-5px);
}

.stat-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.stat-icon.sales {
    background: linear-gradient(135deg, #dc3545 0%, #1e3a8a 100%);
}

.stat-icon.purchases {
    background: linear-gradient(135deg, #1e3a8a 0%, #dc3545 100%);
}

.stat-icon.customers {
    background: linear-gradient(135deg, #dc3545 0%, #1e40af 100%);
}

.stat-icon.profit {
    background: linear-gradient(135deg, #1e40af 0%, #dc3545 100%);
}

.stat-info h3 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.stat-value {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.25rem;
}

.stat-change {
    font-size: 0.8rem;
    font-weight: 600;
}

.stat-change.positive {
    color: #28a745;
}

.stat-change.negative {
    color: #dc3545;
}

/* Dashboard Charts */
.dashboard-charts {
    display: grid;
    grid-template-columns: 2fr 1fr;
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.chart-container {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.chart-container h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

/* Recent Activities */
.recent-activities {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.recent-activities h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.activity-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.activity-item {
    display: flex;
    align-items: flex-start;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
}

.activity-icon {
    width: 40px;
    height: 40px;
    background: #dc3545;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-size: 1rem;
    flex-shrink: 0;
}

.activity-content {
    flex: 1;
}

.activity-content p {
    margin-bottom: 0.25rem;
    color: #333;
}

.activity-time {
    font-size: 0.8rem;
    color: #666;
}

/* Section Header */
.section-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 2rem;
}

.section-header h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #333;
}

/* Buttons */
.btn {
    padding: 0.75rem 1.5rem;
    border: none;
    border-radius: 8px;
    font-size: 0.9rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    text-decoration: none;
}

.btn-primary {
    background: linear-gradient(135deg, #dc3545 0%, #1e3a8a 100%);
    color: white;
}

.btn-primary:hover {
    transform: translateY(-2px);
    box-shadow: 0 5px 15px rgba(220, 53, 69, 0.3);
}

.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-secondary:hover {
    background: #5a6268;
    transform: translateY(-2px);
}

/* Filters Bar */
.filters-bar {
    background: white;
    padding: 1.5rem;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 1.5rem;
    display: flex;
    align-items: end;
    gap: 1rem;
    flex-wrap: wrap;
}

.filter-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.filter-group label {
    font-size: 0.9rem;
    font-weight: 500;
    color: #333;
}

.filter-input,
.filter-select {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    font-family: 'Cairo', sans-serif;
    transition: border-color 0.3s ease;
    min-width: 150px;
}

.filter-input:focus,
.filter-select:focus {
    outline: none;
    border-color: #dc3545;
}

/* Data Table */
.data-table-container {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

.data-table {
    width: 100%;
    border-collapse: collapse;
}

.data-table thead {
    background: #f8f9fa;
}

.data-table th,
.data-table td {
    padding: 1rem;
    text-align: right;
    border-bottom: 1px solid #e9ecef;
}

.data-table th {
    font-weight: 600;
    color: #333;
    font-size: 0.9rem;
}

.data-table td {
    color: #666;
}

.data-table tbody tr:hover {
    background: #f8f9fa;
}

/* Status Badges */
.status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.status.completed {
    background: #d4edda;
    color: #155724;
}

.status.pending {
    background: #fff3cd;
    color: #856404;
}

.status.cancelled {
    background: #f8d7da;
    color: #721c24;
}

/* Action Buttons */
.action-btn-sm {
    background: none;
    border: none;
    padding: 0.5rem;
    border-radius: 5px;
    cursor: pointer;
    font-size: 0.9rem;
    margin: 0 0.25rem;
    transition: all 0.3s ease;
}

.action-btn-sm.view {
    color: #007bff;
}

.action-btn-sm.view:hover {
    background: rgba(0, 123, 255, 0.1);
}

.action-btn-sm.edit {
    color: #28a745;
}

.action-btn-sm.edit:hover {
    background: rgba(40, 167, 69, 0.1);
}

.action-btn-sm.delete {
    color: #dc3545;
}

.action-btn-sm.delete:hover {
    background: rgba(220, 53, 69, 0.1);
}

/* Loading States */
.loading {
    opacity: 0.6;
    pointer-events: none;
    position: relative;
}

.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 30px;
    height: 30px;
    border: 3px solid #f3f3f3;
    border-top: 3px solid #dc3545;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    padding: 1rem 1.5rem;
    z-index: 10000;
    animation: slideInDown 0.3s ease;
}

.notification.success {
    border-left: 4px solid #28a745;
}

.notification.error {
    border-left: 4px solid #dc3545;
}

.notification.warning {
    border-left: 4px solid #ffc107;
}

@keyframes slideInDown {
    from {
        opacity: 0;
        transform: translate(-50%, -100%);
    }
    to {
        opacity: 1;
        transform: translate(-50%, 0);
    }
}

/* Modal Styles */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
}

.modal.active {
    display: flex;
}

.modal-content {
    background: white;
    border-radius: 12px;
    padding: 2rem;
    max-width: 500px;
    width: 90%;
    max-height: 80vh;
    overflow-y: auto;
    animation: modalSlideIn 0.3s ease;
}

@keyframes modalSlideIn {
    from {
        opacity: 0;
        transform: scale(0.8);
    }
    to {
        opacity: 1;
        transform: scale(1);
    }
}

.modal-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1.5rem;
    padding-bottom: 1rem;
    border-bottom: 1px solid #e9ecef;
}

.modal-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
}

.modal-close {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    transition: color 0.3s ease;
}

.modal-close:hover {
    color: #333;
}

/* Form Styles */
.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.form-input,
.form-select,
.form-textarea {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    font-family: 'Cairo', sans-serif;
    transition: border-color 0.3s ease;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

/* Advanced Table Features */
.table-actions {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
    padding: 1rem;
    background: white;
    border-radius: 8px;
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
}

.table-search {
    display: flex;
    align-items: center;
    gap: 0.5rem;
    flex: 1;
    max-width: 300px;
}

.search-input {
    flex: 1;
    padding: 0.5rem 0.75rem;
    border: 1px solid #ddd;
    border-radius: 6px;
    font-size: 0.9rem;
}

.table-controls {
    display: flex;
    align-items: center;
    gap: 0.5rem;
}

.table-pagination {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-top: 1rem;
    padding: 1rem;
}

.pagination-btn {
    padding: 0.5rem 0.75rem;
    border: 1px solid #ddd;
    background: white;
    border-radius: 4px;
    cursor: pointer;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.pagination-btn:hover:not(:disabled) {
    background: #f8f9fa;
    border-color: #dc3545;
}

.pagination-btn:disabled {
    opacity: 0.5;
    cursor: not-allowed;
}

.pagination-btn.active {
    background: #dc3545;
    color: white;
    border-color: #dc3545;
}

/* Responsive Design */
@media (max-width: 1024px) {
    .sidebar {
        transform: translateX(100%);
    }

    .sidebar.active {
        transform: translateX(0);
    }

    .main-content {
        margin-right: 0;
    }

    .mobile-menu-btn {
        display: block;
    }

    .dashboard-charts {
        grid-template-columns: 1fr;
    }

    .table-actions {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .table-search {
        max-width: none;
    }
}

@media (max-width: 768px) {
    .content-area {
        padding: 1rem;
    }

    .header {
        padding: 1rem;
    }

    .dashboard-stats {
        grid-template-columns: 1fr;
    }

    .filters-bar {
        flex-direction: column;
        align-items: stretch;
    }

    .filter-group {
        width: 100%;
    }

    .data-table-container {
        overflow-x: auto;
    }

    .section-header {
        flex-direction: column;
        align-items: stretch;
        gap: 1rem;
    }

    .stat-card {
        flex-direction: column;
        text-align: center;
    }

    .modal-content {
        margin: 1rem;
        padding: 1.5rem;
    }

    .form-row {
        grid-template-columns: 1fr;
    }
}

/* Purchases Section Styles */
.purchases-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

/* Operations Section Styles */
.operations-overview {
    margin-bottom: 2rem;
}

.overview-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.overview-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.overview-card:hover {
    transform: translateY(-5px);
}

.card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.card-header h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
}

.card-header i {
    font-size: 1.5rem;
    color: #dc3545;
}

.big-number {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 1rem;
}

.card-details {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.detail-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.detail-label {
    font-size: 0.9rem;
    color: #666;
}

.detail-value {
    font-weight: 600;
    padding: 0.25rem 0.5rem;
    border-radius: 4px;
    font-size: 0.9rem;
}

.detail-value.success {
    background: #d4edda;
    color: #155724;
}

.detail-value.warning {
    background: #fff3cd;
    color: #856404;
}

.detail-value.danger {
    background: #f8d7da;
    color: #721c24;
}

/* Maintenance Schedule */
.maintenance-schedule {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.maintenance-schedule h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.schedule-grid {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.schedule-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    border-radius: 8px;
    border-left: 4px solid transparent;
    transition: all 0.3s ease;
}

.schedule-item.urgent {
    background: #fff5f5;
    border-left-color: #e53e3e;
}

.schedule-item.normal {
    background: #f7fafc;
    border-left-color: #4299e1;
}

.schedule-icon {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    flex-shrink: 0;
}

.schedule-item.urgent .schedule-icon {
    background: #fed7d7;
    color: #e53e3e;
}

.schedule-item.normal .schedule-icon {
    background: #bee3f8;
    color: #4299e1;
}

.schedule-content {
    flex: 1;
}

.schedule-content h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.schedule-content p {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.25rem;
}

.schedule-date {
    font-size: 0.8rem;
    color: #999;
}

.schedule-status {
    padding: 0.25rem 0.75rem;
    border-radius: 20px;
    font-size: 0.8rem;
    font-weight: 600;
}

.schedule-status.urgent {
    background: #fed7d7;
    color: #e53e3e;
}

.schedule-status.normal {
    background: #bee3f8;
    color: #4299e1;
}

/* Finance Section Styles */
.finance-overview {
    margin-bottom: 2rem;
}

.finance-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.finance-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    display: flex;
    align-items: center;
    gap: 1rem;
    transition: transform 0.3s ease;
}

.finance-card:hover {
    transform: translateY(-5px);
}

.finance-card .card-icon {
    width: 60px;
    height: 60px;
    border-radius: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.5rem;
    color: white;
}

.finance-card.revenue .card-icon {
    background: linear-gradient(135deg, #43e97b 0%, #38f9d7 100%);
}

.finance-card.expenses .card-icon {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
}

.finance-card.profit .card-icon {
    background: linear-gradient(135deg, #1e3a8a 0%, #dc3545 100%);
}

.finance-card .card-content h3 {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 0.5rem;
}

.finance-card .amount {
    font-size: 1.5rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.25rem;
}

.finance-card .change {
    font-size: 0.8rem;
    font-weight: 600;
}

.finance-card .change.positive {
    color: #28a745;
}

.finance-card .change.negative {
    color: #dc3545;
}

/* Financial Reports */
.financial-reports {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.report-section h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.report-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1rem;
}

.report-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.report-item:hover {
    background: #e9ecef;
}

.report-item i {
    font-size: 1.5rem;
    color: #dc3545;
}

.report-item span {
    flex: 1;
    font-weight: 500;
    color: #333;
}

.btn-sm {
    padding: 0.5rem 1rem;
    font-size: 0.8rem;
    border-radius: 6px;
    border: none;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    font-weight: 500;
    transition: all 0.3s ease;
}

.btn-sm.btn-primary {
    background: #dc3545;
    color: white;
}

.btn-sm.btn-primary:hover {
    background: #c82333;
}

.btn-sm.btn-secondary {
    background: #6c757d;
    color: white;
}

.btn-sm.btn-secondary:hover {
    background: #5a6268;
}

/* Customers Section Styles */
.customers-stats {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
    margin-bottom: 2rem;
}

.customers-table-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Payroll Section Styles */
.payroll-summary {
    margin-bottom: 2rem;
}

.summary-cards {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
    gap: 1.5rem;
}

.summary-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.summary-card:hover {
    transform: translateY(-5px);
}

.summary-card .card-header {
    display: flex;
    align-items: center;
    justify-content: space-between;
    margin-bottom: 1rem;
}

.summary-card .card-header h3 {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
}

.summary-card .card-header i {
    font-size: 1.5rem;
    color: #dc3545;
}

.card-amount {
    font-size: 2rem;
    font-weight: 700;
    color: #333;
    margin-bottom: 0.5rem;
}

.summary-card .card-details {
    font-size: 0.9rem;
    color: #666;
}

.payroll-table-section {
    background: white;
    border-radius: 12px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    overflow: hidden;
}

/* Documents Section Styles */
.documents-categories {
    margin-bottom: 2rem;
}

.category-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    gap: 1.5rem;
}

.category-card {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    text-align: center;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    transition: transform 0.3s ease;
}

.category-card:hover {
    transform: translateY(-5px);
}

.category-icon {
    width: 60px;
    height: 60px;
    background: linear-gradient(135deg, #dc3545 0%, #1e3a8a 100%);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto 1rem;
    font-size: 1.5rem;
    color: white;
}

.category-card h3 {
    font-size: 1.1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.5rem;
}

.category-count {
    font-size: 0.9rem;
    color: #666;
    margin-bottom: 1rem;
}

.recent-documents {
    background: white;
    border-radius: 12px;
    padding: 1.5rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.recent-documents h3 {
    font-size: 1.2rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 1rem;
}

.documents-list {
    display: flex;
    flex-direction: column;
    gap: 1rem;
}

.document-item {
    display: flex;
    align-items: center;
    gap: 1rem;
    padding: 1rem;
    background: #f8f9fa;
    border-radius: 8px;
    transition: background 0.3s ease;
}

.document-item:hover {
    background: #e9ecef;
}

.document-icon {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 1.2rem;
    color: white;
    flex-shrink: 0;
}

.document-item:nth-child(odd) .document-icon {
    background: #dc3545;
}

.document-item:nth-child(even) .document-icon {
    background: #007bff;
}

.document-info {
    flex: 1;
}

.document-info h4 {
    font-size: 1rem;
    font-weight: 600;
    color: #333;
    margin-bottom: 0.25rem;
}

.document-info p {
    font-size: 0.8rem;
    color: #666;
}

.document-actions {
    display: flex;
    gap: 0.5rem;
}

/* Enhanced Modal Styles */
.modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.5);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10000;
    animation: fadeIn 0.3s ease;
}

.modal-overlay.active {
    display: flex;
}

.modal-dialog {
    background: white;
    border-radius: 12px;
    max-width: 600px;
    width: 90%;
    max-height: 90vh;
    overflow-y: auto;
    animation: slideInUp 0.3s ease;
}

.modal-header {
    padding: 1.5rem;
    border-bottom: 1px solid #e9ecef;
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: 1.3rem;
    font-weight: 600;
    color: #333;
    margin: 0;
}

.modal-close-btn {
    background: none;
    border: none;
    font-size: 1.5rem;
    color: #666;
    cursor: pointer;
    padding: 0.5rem;
    border-radius: 50%;
    transition: all 0.3s ease;
}

.modal-close-btn:hover {
    background: #f8f9fa;
    color: #333;
}

.modal-body {
    padding: 1.5rem;
}

.modal-footer {
    padding: 1rem 1.5rem;
    border-top: 1px solid #e9ecef;
    display: flex;
    gap: 1rem;
    justify-content: flex-end;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

/* Enhanced Responsive Design */
@media (max-width: 1200px) {
    .finance-cards {
        grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
    }

    .overview-cards {
        grid-template-columns: 1fr;
    }
}

@media (max-width: 768px) {
    .purchases-stats,
    .customers-stats {
        grid-template-columns: 1fr;
    }

    .summary-cards {
        grid-template-columns: 1fr;
    }

    .category-grid {
        grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    }

    .finance-cards {
        grid-template-columns: 1fr;
    }

    .report-grid {
        grid-template-columns: 1fr;
    }

    .document-item {
        flex-direction: column;
        text-align: center;
    }

    .document-actions {
        justify-content: center;
    }

    .modal-dialog {
        margin: 1rem;
        width: calc(100% - 2rem);
    }

    .modal-header,
    .modal-body,
    .modal-footer {
        padding: 1rem;
    }
}

/* Alert Styles */
.alert {
    padding: 1rem;
    border-radius: 8px;
    margin: 1rem 0;
    display: flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
}

.alert-info {
    background: #d1ecf1;
    border: 1px solid #bee5eb;
    color: #0c5460;
}

.alert-success {
    background: #d4edda;
    border: 1px solid #c3e6cb;
    color: #155724;
}

.alert-warning {
    background: #fff3cd;
    border: 1px solid #ffeaa7;
    color: #856404;
}

.alert-danger {
    background: #f8d7da;
    border: 1px solid #f5c6cb;
    color: #721c24;
}

/* Enhanced Notification Styles */
.notification {
    position: fixed;
    top: 20px;
    left: 50%;
    transform: translateX(-50%);
    background: white;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0,0,0,0.15);
    z-index: 10001;
    animation: slideInDown 0.3s ease;
    min-width: 300px;
    max-width: 500px;
}

.notification-content {
    display: flex;
    align-items: center;
    gap: 0.75rem;
    padding: 1rem 1.5rem;
}

.notification.success {
    border-left: 4px solid #28a745;
}

.notification.error {
    border-left: 4px solid #dc3545;
}

.notification.warning {
    border-left: 4px solid #ffc107;
}

.notification.info {
    border-left: 4px solid #17a2b8;
}

.notification-close {
    background: none;
    border: none;
    cursor: pointer;
    margin-left: auto;
    opacity: 0.7;
    transition: opacity 0.3s ease;
    padding: 0.25rem;
    border-radius: 4px;
}

.notification-close:hover {
    opacity: 1;
    background: rgba(0,0,0,0.1);
}

/* Enhanced Form Styles */
.modal-form {
    width: 100%;
}

.form-row {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 1rem;
    margin-bottom: 1rem;
}

.form-group {
    display: flex;
    flex-direction: column;
    gap: 0.5rem;
}

.form-label {
    font-weight: 500;
    color: #333;
    font-size: 0.9rem;
}

.form-input,
.form-select,
.form-textarea {
    padding: 0.75rem;
    border: 2px solid #e9ecef;
    border-radius: 8px;
    font-size: 0.9rem;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    background: white;
}

.form-input:focus,
.form-select:focus,
.form-textarea:focus {
    outline: none;
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    transform: translateY(-1px);
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-input[type="file"] {
    padding: 0.5rem;
}

/* Loading States */
.btn.loading {
    position: relative;
    color: transparent;
    pointer-events: none;
}

.btn.loading::after {
    content: '';
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 16px;
    height: 16px;
    border: 2px solid transparent;
    border-top: 2px solid currentColor;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Enhanced Table Features */
.table-wrapper {
    position: relative;
}

.table-loading {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(255, 255, 255, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 10;
}

.table-loading.active {
    display: flex;
}

.loading-spinner {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 1rem;
    color: #dc3545;
}

.spinner {
    width: 40px;
    height: 40px;
    border: 4px solid #f3f3f3;
    border-top: 4px solid #dc3545;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 3rem 1rem;
    color: #666;
}

.empty-state-icon {
    font-size: 3rem;
    color: #ddd;
    margin-bottom: 1rem;
}

.empty-state h3 {
    font-size: 1.2rem;
    font-weight: 600;
    margin-bottom: 0.5rem;
    color: #333;
}

.empty-state p {
    font-size: 0.9rem;
    margin-bottom: 1.5rem;
}

/* Tooltip Styles */
.tooltip {
    position: relative;
    cursor: help;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: #333;
    color: white;
    padding: 0.5rem;
    border-radius: 4px;
    font-size: 0.8rem;
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
    z-index: 1000;
}

.tooltip::after {
    content: '';
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    border: 5px solid transparent;
    border-top-color: #333;
    opacity: 0;
    visibility: hidden;
    transition: all 0.3s ease;
}

.tooltip:hover::before,
.tooltip:hover::after {
    opacity: 1;
    visibility: visible;
    transform: translateX(-50%) translateY(-5px);
}

/* Progress Bar */
.progress {
    width: 100%;
    height: 8px;
    background: #e9ecef;
    border-radius: 4px;
    overflow: hidden;
    margin: 1rem 0;
}

.progress-bar {
    height: 100%;
    background: linear-gradient(135deg, #dc3545 0%, #1e3a8a 100%);
    border-radius: 4px;
    transition: width 0.3s ease;
    position: relative;
}

.progress-bar.animated {
    background-size: 20px 20px;
    background-image: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.15) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.15) 50%,
        rgba(255, 255, 255, 0.15) 75%,
        transparent 75%,
        transparent
    );
    animation: progressAnimation 1s linear infinite;
}

@keyframes progressAnimation {
    0% {
        background-position: 0 0;
    }
    100% {
        background-position: 20px 0;
    }
}

/* Badge Styles */
.badge {
    display: inline-block;
    padding: 0.25rem 0.5rem;
    font-size: 0.75rem;
    font-weight: 600;
    border-radius: 4px;
    text-align: center;
    white-space: nowrap;
}

.badge-primary {
    background: #dc3545;
    color: white;
}

.badge-success {
    background: #28a745;
    color: white;
}

.badge-warning {
    background: #ffc107;
    color: #212529;
}

.badge-danger {
    background: #dc3545;
    color: white;
}

.badge-info {
    background: #17a2b8;
    color: white;
}

.badge-light {
    background: #f8f9fa;
    color: #495057;
    border: 1px solid #dee2e6;
}

/* Animations */
@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.05);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -30px, 0);
    }
    70% {
        transform: translate3d(0, -15px, 0);
    }
    90% {
        transform: translate3d(0, -4px, 0);
    }
}

.pulse {
    animation: pulse 2s infinite;
}

.bounce {
    animation: bounce 1s;
}
