<!DOCTYPE html>
<html lang="ar" dir="rtl">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>تسجيل الدخول - نجوم الحزم</title>
    <link rel="stylesheet" href="css/login.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Cairo:wght@300;400;600;700&display=swap" rel="stylesheet">
</head>
<body>
    <div class="login-container">
        <div class="login-background">
            <div class="shape shape1"></div>
            <div class="shape shape2"></div>
            <div class="shape shape3"></div>
        </div>
        
        <div class="login-card">
            <div class="login-header">
                <div class="logo">
                    <i class="fas fa-fire-extinguisher logo-icon"></i>
                    <h1>نجوم الحزم</h1>
                </div>
                <p class="login-subtitle">نظام إدارة الأعمال</p>
            </div>
            
            <form class="login-form" id="loginForm">
                <div class="form-group">
                    <div class="input-container">
                        <i class="fas fa-user input-icon"></i>
                        <input type="text" id="username" placeholder="اسم المستخدم" required>
                    </div>
                </div>
                
                <div class="form-group">
                    <div class="input-container">
                        <i class="fas fa-lock input-icon"></i>
                        <input type="password" id="password" placeholder="كلمة المرور" required>
                        <i class="fas fa-eye toggle-password" id="togglePassword"></i>
                    </div>
                </div>
                
                <div class="form-options">
                    <label class="remember-me">
                        <input type="checkbox" id="rememberMe">
                        <span class="checkmark"></span>
                        تذكرني
                    </label>
                    <a href="#" class="forgot-password">نسيت كلمة المرور؟</a>
                </div>
                
                <button type="submit" class="login-btn">
                    <span class="btn-text">تسجيل الدخول</span>
                    <i class="fas fa-arrow-left btn-icon"></i>
                </button>
                
                <div class="login-footer">
                    <p>ليس لديك حساب؟ <a href="#" class="register-link">إنشاء حساب جديد</a></p>
                </div>
            </form>
            
            <div class="back-to-home">
                <a href="index.html" class="back-link">
                    <i class="fas fa-arrow-right"></i>
                    العودة للرئيسية
                </a>
            </div>
        </div>
        
        <!-- Demo Credentials Info -->
        <div class="demo-info">
            <h4>بيانات تجريبية للدخول:</h4>
            <p><strong>المستخدم:</strong> admin</p>
            <p><strong>كلمة المرور:</strong> admin123</p>
        </div>
    </div>
    
    <!-- Loading Overlay -->
    <div class="loading-overlay" id="loadingOverlay">
        <div class="loading-spinner">
            <i class="fas fa-fire-extinguisher spinning"></i>
            <p>جاري تسجيل الدخول...</p>
        </div>
    </div>
    
    <!-- Success Message -->
    <div class="success-message" id="successMessage">
        <div class="success-content">
            <i class="fas fa-check-circle"></i>
            <h3>تم تسجيل الدخول بنجاح!</h3>
            <p>سيتم توجيهك إلى لوحة التحكم...</p>
        </div>
    </div>
    
    <!-- Error Message -->
    <div class="error-message" id="errorMessage">
        <div class="error-content">
            <i class="fas fa-exclamation-triangle"></i>
            <h3>خطأ في تسجيل الدخول</h3>
            <p id="errorText">اسم المستخدم أو كلمة المرور غير صحيحة</p>
            <button class="close-error" onclick="closeError()">حسناً</button>
        </div>
    </div>

    <script src="js/login.js"></script>
</body>
</html>
