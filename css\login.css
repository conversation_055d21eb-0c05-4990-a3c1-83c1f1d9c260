/* Reset and Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Cairo', sans-serif;
    direction: rtl;
    text-align: right;
    overflow: hidden;
}

/* Login Container */
.login-container {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    position: relative;
    background: linear-gradient(135deg, #1e3a8a 0%, #dc3545 100%);
}

/* Animated Background */
.login-background {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    overflow: hidden;
    z-index: 1;
}

.shape {
    position: absolute;
    border-radius: 50%;
    background: rgba(255, 255, 255, 0.1);
    animation: float 6s ease-in-out infinite;
}

.shape1 {
    width: 200px;
    height: 200px;
    top: 10%;
    left: 10%;
    animation-delay: 0s;
}

.shape2 {
    width: 150px;
    height: 150px;
    top: 60%;
    right: 15%;
    animation-delay: 2s;
}

.shape3 {
    width: 100px;
    height: 100px;
    bottom: 20%;
    left: 20%;
    animation-delay: 4s;
}

/* Login Card */
.login-card {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: 0 25px 50px rgba(0, 0, 0, 0.2);
    width: 100%;
    max-width: 450px;
    position: relative;
    z-index: 2;
    animation: slideInUp 0.8s ease;
}

/* Login Header */
.login-header {
    text-align: center;
    margin-bottom: 2.5rem;
}

.logo {
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 1rem;
    margin-bottom: 1rem;
}

.logo-icon {
    font-size: 3rem;
    color: #dc3545;
    animation: pulse 2s infinite;
}

.logo h1 {
    font-size: 2.5rem;
    font-weight: 700;
    color: #333;
    margin: 0;
}

.login-subtitle {
    color: #666;
    font-size: 1.1rem;
    font-weight: 400;
}

/* Form Styles */
.login-form {
    width: 100%;
}

.form-group {
    margin-bottom: 1.5rem;
}

.input-container {
    position: relative;
    display: flex;
    align-items: center;
}

.input-icon {
    position: absolute;
    right: 1rem;
    color: #dc3545;
    font-size: 1.1rem;
    z-index: 3;
}

.input-container input {
    width: 100%;
    padding: 1rem 3rem 1rem 1rem;
    border: 2px solid #e9ecef;
    border-radius: 12px;
    font-size: 1rem;
    font-family: 'Cairo', sans-serif;
    transition: all 0.3s ease;
    background: rgba(255, 255, 255, 0.9);
}

.input-container input:focus {
    outline: none;
    border-color: #dc3545;
    box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
    transform: translateY(-2px);
}

.toggle-password {
    position: absolute;
    left: 1rem;
    cursor: pointer;
    color: #999;
    transition: color 0.3s ease;
}

.toggle-password:hover {
    color: #dc3545;
}

/* Form Options */
.form-options {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 2rem;
    font-size: 0.9rem;
}

.remember-me {
    display: flex;
    align-items: center;
    cursor: pointer;
    color: #666;
}

.remember-me input[type="checkbox"] {
    display: none;
}

.checkmark {
    width: 18px;
    height: 18px;
    border: 2px solid #ddd;
    border-radius: 4px;
    margin-left: 0.5rem;
    position: relative;
    transition: all 0.3s ease;
}

.remember-me input[type="checkbox"]:checked + .checkmark {
    background: #dc3545;
    border-color: #dc3545;
}

.remember-me input[type="checkbox"]:checked + .checkmark::after {
    content: '\f00c';
    font-family: 'Font Awesome 6 Free';
    font-weight: 900;
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    color: white;
    font-size: 10px;
}

.forgot-password {
    color: #1e3a8a;
    text-decoration: none;
    transition: color 0.3s ease;
}

.forgot-password:hover {
    color: #1e40af;
    text-decoration: underline;
}

/* Login Button */
.login-btn {
    width: 100%;
    padding: 1rem;
    background: linear-gradient(135deg, #dc3545 0%, #1e3a8a 100%);
    border: none;
    border-radius: 12px;
    color: white;
    font-size: 1.1rem;
    font-weight: 600;
    font-family: 'Cairo', sans-serif;
    cursor: pointer;
    transition: all 0.3s ease;
    display: flex;
    align-items: center;
    justify-content: center;
    gap: 0.5rem;
    margin-bottom: 1.5rem;
}

.login-btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 10px 25px rgba(220, 53, 69, 0.3);
}

.login-btn:active {
    transform: translateY(0);
}

.btn-icon {
    transition: transform 0.3s ease;
}

.login-btn:hover .btn-icon {
    transform: translateX(-3px);
}

/* Login Footer */
.login-footer {
    text-align: center;
    color: #666;
    font-size: 0.9rem;
}

.register-link {
    color: #dc3545;
    text-decoration: none;
    font-weight: 600;
    transition: color 0.3s ease;
}

.register-link:hover {
    color: #c82333;
    text-decoration: underline;
}

/* Back to Home */
.back-to-home {
    text-align: center;
    margin-top: 1.5rem;
}

.back-link {
    color: #666;
    text-decoration: none;
    display: inline-flex;
    align-items: center;
    gap: 0.5rem;
    font-size: 0.9rem;
    transition: all 0.3s ease;
}

.back-link:hover {
    color: #333;
    transform: translateX(3px);
}

/* Demo Info */
.demo-info {
    position: fixed;
    top: 20px;
    left: 20px;
    background: rgba(255, 255, 255, 0.9);
    backdrop-filter: blur(10px);
    padding: 1rem;
    border-radius: 10px;
    box-shadow: 0 5px 15px rgba(0, 0, 0, 0.1);
    font-size: 0.85rem;
    color: #333;
    z-index: 3;
}

.demo-info h4 {
    margin-bottom: 0.5rem;
    color: #dc3545;
    font-weight: 600;
}

.demo-info p {
    margin: 0.25rem 0;
}

/* Loading Overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.loading-spinner {
    text-align: center;
    color: white;
}

.spinning {
    font-size: 3rem;
    color: #dc3545;
    animation: spin 1s linear infinite;
}

.loading-spinner p {
    margin-top: 1rem;
    font-size: 1.1rem;
}

/* Success Message */
.success-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.success-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    animation: bounceIn 0.6s ease;
}

.success-content i {
    font-size: 3rem;
    color: #28a745;
    margin-bottom: 1rem;
}

.success-content h3 {
    color: #333;
    margin-bottom: 0.5rem;
}

.success-content p {
    color: #666;
}

/* Error Message */
.error-message {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: rgba(0, 0, 0, 0.8);
    display: none;
    align-items: center;
    justify-content: center;
    z-index: 9999;
}

.error-content {
    background: white;
    padding: 2rem;
    border-radius: 15px;
    text-align: center;
    animation: shake 0.6s ease;
}

.error-content i {
    font-size: 3rem;
    color: #dc3545;
    margin-bottom: 1rem;
}

.error-content h3 {
    color: #333;
    margin-bottom: 0.5rem;
}

.error-content p {
    color: #666;
    margin-bottom: 1.5rem;
}

.close-error {
    background: #dc3545;
    color: white;
    border: none;
    padding: 0.5rem 1.5rem;
    border-radius: 8px;
    cursor: pointer;
    font-family: 'Cairo', sans-serif;
    transition: background 0.3s ease;
}

.close-error:hover {
    background: #c82333;
}

/* Animations */
@keyframes float {
    0%, 100% {
        transform: translateY(0px);
    }
    50% {
        transform: translateY(-20px);
    }
}

@keyframes slideInUp {
    from {
        opacity: 0;
        transform: translateY(50px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% {
        transform: scale(1);
    }
    50% {
        transform: scale(1.1);
    }
}

@keyframes spin {
    from {
        transform: rotate(0deg);
    }
    to {
        transform: rotate(360deg);
    }
}

@keyframes bounceIn {
    0% {
        opacity: 0;
        transform: scale(0.3);
    }
    50% {
        opacity: 1;
        transform: scale(1.05);
    }
    70% {
        transform: scale(0.9);
    }
    100% {
        opacity: 1;
        transform: scale(1);
    }
}

@keyframes shake {
    0%, 100% {
        transform: translateX(0);
    }
    10%, 30%, 50%, 70%, 90% {
        transform: translateX(-10px);
    }
    20%, 40%, 60%, 80% {
        transform: translateX(10px);
    }
}

/* Responsive Design */
@media (max-width: 768px) {
    .login-card {
        margin: 1rem;
        padding: 2rem;
    }
    
    .demo-info {
        position: relative;
        margin: 1rem;
        top: auto;
        left: auto;
    }
    
    body {
        overflow: auto;
    }
    
    .login-container {
        min-height: auto;
        padding: 2rem 0;
    }
}
