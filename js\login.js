// Login Page JavaScript for Stars of Packages

document.addEventListener('DOMContentLoaded', function() {
    initializeLoginForm();
    initializePasswordToggle();
    initializeFormValidation();
});

// Demo credentials
const DEMO_CREDENTIALS = {
    username: 'admin',
    password: 'admin123'
};

// Initialize login form
function initializeLoginForm() {
    const loginForm = document.getElementById('loginForm');
    const loadingOverlay = document.getElementById('loadingOverlay');
    const successMessage = document.getElementById('successMessage');
    const errorMessage = document.getElementById('errorMessage');

    if (loginForm) {
        loginForm.addEventListener('submit', function(e) {
            e.preventDefault();
            
            const username = document.getElementById('username').value.trim();
            const password = document.getElementById('password').value.trim();
            const rememberMe = document.getElementById('rememberMe').checked;
            
            // Validate inputs
            if (!username || !password) {
                showError('يرجى إدخال اسم المستخدم وكلمة المرور');
                return;
            }
            
            // Show loading
            showLoading();
            
            // Simulate authentication delay
            setTimeout(() => {
                if (authenticateUser(username, password)) {
                    // Store login state
                    if (rememberMe) {
                        localStorage.setItem('starsOfPackages_rememberMe', 'true');
                        localStorage.setItem('starsOfPackages_username', username);
                    }
                    sessionStorage.setItem('starsOfPackages_loggedIn', 'true');
                    sessionStorage.setItem('starsOfPackages_user', JSON.stringify({
                        username: username,
                        loginTime: new Date().toISOString(),
                        role: 'admin'
                    }));
                    
                    hideLoading();
                    showSuccess();
                    
                    // Redirect to dashboard after success message
                    setTimeout(() => {
                        window.location.href = 'dashboard.html';
                    }, 2000);
                } else {
                    hideLoading();
                    showError('اسم المستخدم أو كلمة المرور غير صحيحة');
                }
            }, 1500);
        });
    }
    
    // Check if user should be remembered
    checkRememberedUser();
}

// Authenticate user (demo implementation)
function authenticateUser(username, password) {
    return username === DEMO_CREDENTIALS.username && password === DEMO_CREDENTIALS.password;
}

// Check for remembered user
function checkRememberedUser() {
    const rememberMe = localStorage.getItem('starsOfPackages_rememberMe');
    const savedUsername = localStorage.getItem('starsOfPackages_username');
    
    if (rememberMe === 'true' && savedUsername) {
        document.getElementById('username').value = savedUsername;
        document.getElementById('rememberMe').checked = true;
    }
}

// Password toggle functionality
function initializePasswordToggle() {
    const togglePassword = document.getElementById('togglePassword');
    const passwordInput = document.getElementById('password');
    
    if (togglePassword && passwordInput) {
        togglePassword.addEventListener('click', function() {
            const type = passwordInput.getAttribute('type') === 'password' ? 'text' : 'password';
            passwordInput.setAttribute('type', type);
            
            // Toggle icon
            this.classList.toggle('fa-eye');
            this.classList.toggle('fa-eye-slash');
        });
    }
}

// Form validation
function initializeFormValidation() {
    const inputs = document.querySelectorAll('#loginForm input');
    
    inputs.forEach(input => {
        // Real-time validation
        input.addEventListener('blur', function() {
            validateInput(this);
        });
        
        input.addEventListener('input', function() {
            // Remove error styling on input
            this.classList.remove('error');
            const errorMsg = this.parentElement.querySelector('.error-message');
            if (errorMsg) {
                errorMsg.remove();
            }
        });
    });
}

// Validate individual input
function validateInput(input) {
    const value = input.value.trim();
    let isValid = true;
    let errorMessage = '';
    
    // Remove existing error
    input.classList.remove('error');
    const existingError = input.parentElement.querySelector('.error-message');
    if (existingError) {
        existingError.remove();
    }
    
    // Validation rules
    if (input.type === 'text' && input.id === 'username') {
        if (!value) {
            isValid = false;
            errorMessage = 'اسم المستخدم مطلوب';
        } else if (value.length < 3) {
            isValid = false;
            errorMessage = 'اسم المستخدم يجب أن يكون 3 أحرف على الأقل';
        }
    }
    
    if (input.type === 'password') {
        if (!value) {
            isValid = false;
            errorMessage = 'كلمة المرور مطلوبة';
        } else if (value.length < 6) {
            isValid = false;
            errorMessage = 'كلمة المرور يجب أن تكون 6 أحرف على الأقل';
        }
    }
    
    // Show error if invalid
    if (!isValid) {
        input.classList.add('error');
        showInputError(input, errorMessage);
    }
    
    return isValid;
}

// Show input error
function showInputError(input, message) {
    const errorElement = document.createElement('div');
    errorElement.className = 'error-message';
    errorElement.textContent = message;
    
    // Add error styles
    const errorStyles = `
        .error-message {
            color: #dc3545;
            font-size: 0.8rem;
            margin-top: 0.25rem;
            display: flex;
            align-items: center;
            gap: 0.25rem;
        }
        
        .error-message::before {
            content: '⚠';
        }
        
        .input-container input.error {
            border-color: #dc3545;
            box-shadow: 0 0 0 3px rgba(220, 53, 69, 0.1);
        }
    `;
    
    if (!document.querySelector('#input-error-styles')) {
        const styleSheet = document.createElement('style');
        styleSheet.id = 'input-error-styles';
        styleSheet.textContent = errorStyles;
        document.head.appendChild(styleSheet);
    }
    
    input.parentElement.appendChild(errorElement);
}

// Show loading overlay
function showLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'flex';
    }
}

// Hide loading overlay
function hideLoading() {
    const loadingOverlay = document.getElementById('loadingOverlay');
    if (loadingOverlay) {
        loadingOverlay.style.display = 'none';
    }
}

// Show success message
function showSuccess() {
    const successMessage = document.getElementById('successMessage');
    if (successMessage) {
        successMessage.style.display = 'flex';
    }
}

// Show error message
function showError(message) {
    const errorMessage = document.getElementById('errorMessage');
    const errorText = document.getElementById('errorText');
    
    if (errorMessage && errorText) {
        errorText.textContent = message;
        errorMessage.style.display = 'flex';
    }
}

// Close error message
function closeError() {
    const errorMessage = document.getElementById('errorMessage');
    if (errorMessage) {
        errorMessage.style.display = 'none';
    }
}

// Keyboard shortcuts
document.addEventListener('keydown', function(e) {
    // Enter key to submit form
    if (e.key === 'Enter' && e.target.tagName !== 'BUTTON') {
        const loginForm = document.getElementById('loginForm');
        if (loginForm) {
            loginForm.dispatchEvent(new Event('submit'));
        }
    }
    
    // Escape key to close error
    if (e.key === 'Escape') {
        closeError();
    }
});

// Auto-fill demo credentials (for development)
function fillDemoCredentials() {
    document.getElementById('username').value = DEMO_CREDENTIALS.username;
    document.getElementById('password').value = DEMO_CREDENTIALS.password;
}

// Add demo credentials button functionality
document.addEventListener('click', function(e) {
    if (e.target.closest('.demo-info')) {
        fillDemoCredentials();
    }
});

// Prevent form submission on demo info click
document.querySelector('.demo-info')?.addEventListener('click', function(e) {
    e.preventDefault();
    fillDemoCredentials();
});

// Check if already logged in
function checkExistingLogin() {
    const isLoggedIn = sessionStorage.getItem('starsOfPackages_loggedIn');
    if (isLoggedIn === 'true') {
        // Redirect to dashboard if already logged in
        window.location.href = 'dashboard.html';
    }
}

// Check on page load
checkExistingLogin();

// Export functions for global access
window.LoginManager = {
    showError,
    closeError,
    fillDemoCredentials,
    authenticateUser
};
