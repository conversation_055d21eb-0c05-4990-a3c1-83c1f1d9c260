// Dashboard JavaScript for Stars of Packages

document.addEventListener('DOMContentLoaded', function() {
    // Check authentication
    checkAuthentication();
    
    // Initialize dashboard components
    initializeSidebar();
    initializeNavigation();
    initializeCharts();
    initializeDataTables();
    initializeMobileMenu();
});

// Check if user is authenticated
function checkAuthentication() {
    const isLoggedIn = sessionStorage.getItem('starsOfPackages_loggedIn');
    const user = sessionStorage.getItem('starsOfPackages_user');
    
    if (!isLoggedIn || !user) {
        // Redirect to login if not authenticated
        window.location.href = 'login.html';
        return;
    }
    
    // Update user info in sidebar
    const userData = JSON.parse(user);
    updateUserInfo(userData);
}

// Update user information in the interface
function updateUserInfo(userData) {
    const userNameElements = document.querySelectorAll('.user-name');
    const userRoleElements = document.querySelectorAll('.user-role');
    
    userNameElements.forEach(element => {
        element.textContent = userData.username === 'admin' ? 'المدير العام' : userData.username;
    });
    
    userRoleElements.forEach(element => {
        element.textContent = 'مدير النظام';
    });
}

// Initialize sidebar functionality
function initializeSidebar() {
    const sidebarToggle = document.getElementById('sidebarToggle');
    const sidebar = document.getElementById('sidebar');
    const mainContent = document.getElementById('mainContent');
    
    if (sidebarToggle) {
        sidebarToggle.addEventListener('click', function() {
            sidebar.classList.toggle('collapsed');
            mainContent.classList.toggle('expanded');
        });
    }
}

// Initialize navigation between sections
function initializeNavigation() {
    const navLinks = document.querySelectorAll('.nav-link');
    const contentSections = document.querySelectorAll('.content-section');
    const pageTitle = document.getElementById('pageTitle');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetSection = this.getAttribute('data-section');
            
            // Update active nav link
            navLinks.forEach(navLink => navLink.classList.remove('active'));
            this.classList.add('active');
            
            // Show target section
            contentSections.forEach(section => section.classList.remove('active'));
            const targetElement = document.getElementById(`${targetSection}-section`);
            if (targetElement) {
                targetElement.classList.add('active');
            }
            
            // Update page title
            const sectionTitles = {
                'dashboard': 'لوحة التحكم',
                'sales': 'إدارة المبيعات',
                'purchases': 'إدارة المشتريات',
                'operations': 'التشغيل والصيانة',
                'finance': 'الإدارة المالية',
                'customers': 'إدارة العملاء',
                'payroll': 'إدارة الرواتب',
                'documents': 'أوراق الشركة'
            };
            
            if (pageTitle && sectionTitles[targetSection]) {
                pageTitle.textContent = sectionTitles[targetSection];
            }
            
            // Close mobile menu if open
            const sidebar = document.getElementById('sidebar');
            if (window.innerWidth <= 1024) {
                sidebar.classList.remove('active');
            }
        });
    });
}

// Initialize mobile menu
function initializeMobileMenu() {
    const mobileMenuBtn = document.getElementById('mobileMenuBtn');
    const sidebar = document.getElementById('sidebar');
    
    if (mobileMenuBtn) {
        mobileMenuBtn.addEventListener('click', function() {
            sidebar.classList.toggle('active');
        });
    }
    
    // Close sidebar when clicking outside on mobile
    document.addEventListener('click', function(e) {
        if (window.innerWidth <= 1024) {
            if (!sidebar.contains(e.target) && !mobileMenuBtn.contains(e.target)) {
                sidebar.classList.remove('active');
            }
        }
    });
}

// Initialize charts
function initializeCharts() {
    // Sales Chart
    const salesChartCtx = document.getElementById('salesChart');
    if (salesChartCtx) {
        new Chart(salesChartCtx, {
            type: 'line',
            data: {
                labels: ['يناير', 'فبراير', 'مارس', 'أبريل', 'مايو', 'يونيو'],
                datasets: [{
                    label: 'المبيعات (ريال)',
                    data: [45000, 52000, 48000, 61000, 55000, 67000],
                    borderColor: '#667eea',
                    backgroundColor: 'rgba(102, 126, 234, 0.1)',
                    borderWidth: 3,
                    fill: true,
                    tension: 0.4
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        display: false
                    }
                },
                scales: {
                    y: {
                        beginAtZero: true,
                        ticks: {
                            callback: function(value) {
                                return value.toLocaleString() + ' ريال';
                            }
                        }
                    }
                }
            }
        });
    }
    
    // Category Chart
    const categoryChartCtx = document.getElementById('categoryChart');
    if (categoryChartCtx) {
        new Chart(categoryChartCtx, {
            type: 'doughnut',
            data: {
                labels: ['معدات الإطفاء', 'أنظمة الإنذار', 'الصيانة', 'التدريب'],
                datasets: [{
                    data: [40, 25, 20, 15],
                    backgroundColor: [
                        '#667eea',
                        '#f093fb',
                        '#4facfe',
                        '#43e97b'
                    ],
                    borderWidth: 0
                }]
            },
            options: {
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    }
                }
            }
        });
    }
}

// Initialize data tables
function initializeDataTables() {
    // Add sorting functionality to tables
    const tables = document.querySelectorAll('.data-table');
    
    tables.forEach(table => {
        const headers = table.querySelectorAll('th');
        
        headers.forEach((header, index) => {
            if (index < headers.length - 1) { // Exclude actions column
                header.style.cursor = 'pointer';
                header.addEventListener('click', function() {
                    sortTable(table, index);
                });
            }
        });
    });
}

// Sort table function
function sortTable(table, columnIndex) {
    const tbody = table.querySelector('tbody');
    const rows = Array.from(tbody.querySelectorAll('tr'));
    const isAscending = table.getAttribute('data-sort-direction') !== 'asc';
    
    rows.sort((a, b) => {
        const aValue = a.cells[columnIndex].textContent.trim();
        const bValue = b.cells[columnIndex].textContent.trim();
        
        // Check if values are numbers
        const aNum = parseFloat(aValue.replace(/[^\d.-]/g, ''));
        const bNum = parseFloat(bValue.replace(/[^\d.-]/g, ''));
        
        if (!isNaN(aNum) && !isNaN(bNum)) {
            return isAscending ? aNum - bNum : bNum - aNum;
        }
        
        // String comparison
        return isAscending ? aValue.localeCompare(bValue) : bValue.localeCompare(aValue);
    });
    
    // Clear tbody and append sorted rows
    tbody.innerHTML = '';
    rows.forEach(row => tbody.appendChild(row));
    
    // Update sort direction
    table.setAttribute('data-sort-direction', isAscending ? 'asc' : 'desc');
    
    // Update header indicators
    const headers = table.querySelectorAll('th');
    headers.forEach(header => {
        header.classList.remove('sort-asc', 'sort-desc');
    });
    headers[columnIndex].classList.add(isAscending ? 'sort-asc' : 'sort-desc');
}

// Logout functionality
function logout() {
    // Clear session data
    sessionStorage.removeItem('starsOfPackages_loggedIn');
    sessionStorage.removeItem('starsOfPackages_user');
    
    // Show confirmation
    if (confirm('هل أنت متأكد من تسجيل الخروج؟')) {
        // Redirect to login page
        window.location.href = 'login.html';
    }
}

// Add new record functionality
function addNewRecord(type) {
    // This would typically open a modal or navigate to a form
    alert(`إضافة ${type} جديد - هذه الميزة قيد التطوير`);
}

// View record functionality
function viewRecord(id, type) {
    alert(`عرض ${type} رقم ${id} - هذه الميزة قيد التطوير`);
}

// Edit record functionality
function editRecord(id, type) {
    alert(`تعديل ${type} رقم ${id} - هذه الميزة قيد التطوير`);
}

// Delete record functionality
function deleteRecord(id, type) {
    if (confirm(`هل أنت متأكد من حذف ${type} رقم ${id}؟`)) {
        alert(`تم حذف ${type} رقم ${id}`);
        // Here you would typically make an API call to delete the record
    }
}

// Search functionality
function performSearch(searchTerm, filters) {
    console.log('البحث عن:', searchTerm, 'مع الفلاتر:', filters);
    // This would typically make an API call to search records
}

// Export data functionality
function exportData(format, section) {
    alert(`تصدير بيانات ${section} بصيغة ${format} - هذه الميزة قيد التطوير`);
}

// Real-time updates simulation
function simulateRealTimeUpdates() {
    setInterval(() => {
        // Update statistics randomly
        const statValues = document.querySelectorAll('.stat-value');
        statValues.forEach(stat => {
            const currentValue = parseInt(stat.textContent.replace(/[^\d]/g, ''));
            const change = Math.floor(Math.random() * 1000) - 500;
            const newValue = Math.max(0, currentValue + change);
            stat.textContent = newValue.toLocaleString() + ' ريال';
        });
        
        // Update change indicators
        const statChanges = document.querySelectorAll('.stat-change');
        statChanges.forEach(change => {
            const randomChange = (Math.random() * 20 - 10).toFixed(1);
            const isPositive = randomChange > 0;
            change.textContent = (isPositive ? '+' : '') + randomChange + '%';
            change.className = 'stat-change ' + (isPositive ? 'positive' : 'negative');
        });
    }, 30000); // Update every 30 seconds
}

// Initialize real-time updates
simulateRealTimeUpdates();

// Add CSS for table sorting indicators
const sortingStyles = `
    .data-table th.sort-asc::after {
        content: ' ↑';
        color: #667eea;
    }
    
    .data-table th.sort-desc::after {
        content: ' ↓';
        color: #667eea;
    }
    
    .data-table th:hover {
        background: #e9ecef;
    }
`;

const styleSheet = document.createElement('style');
styleSheet.textContent = sortingStyles;
document.head.appendChild(styleSheet);

// Export functions for global access
window.DashboardManager = {
    logout,
    addNewRecord,
    viewRecord,
    editRecord,
    deleteRecord,
    performSearch,
    exportData
};
